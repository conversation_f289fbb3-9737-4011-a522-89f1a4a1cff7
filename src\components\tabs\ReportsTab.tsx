
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Download, Calendar, RefreshCw, TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import { DemoSwitcher } from '@/components/DemoSwitcher';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface BalanceSheetItem {
  name: string;
  total: number;
}

interface BalanceSheetResponse {
  table: BalanceSheetItem[];
  query: {
    date_range: string;
    number_format: string;
    basis: string;
  };
  metadata: {
    organization_name: string;
    currency: string;
    date_format: string;
  };
}

export const ReportsTab = () => {
  const { session } = useAuth();
  const [fromDate, setFromDate] = useState('2024-01-01');
  const [toDate, setToDate] = useState('2024-12-31');
  const [loading, setLoading] = useState(false);
  const [balanceSheetData, setBalanceSheetData] = useState<BalanceSheetResponse | null>(null);
  const [reportType, setReportType] = useState('balance-sheet');
  const [companyName, setCompanyName] = useState('PT. Example Company');

  // Demo state for role and plan management
  const [userRole, setUserRole] = useState<'admin' | 'general' | 'petty_cash' | 'staff'>('admin');
  const [userPlan, setUserPlan] = useState<'free' | 'premium' | 'enterprise'>('enterprise');

  // For now, only balance sheet is available
  const reportTypes = [
    { value: 'balance-sheet', label: 'Neraca' }
  ];

  // Load saved report settings from Supabase
  const loadReportSettings = async () => {
    if (!session?.user?.id) return;

    try {
      const { data, error } = await supabase.rpc('get_user_report_settings', {
        p_report_type: 'balance-sheet'
      });

      if (error) {
        console.error('Error loading report settings:', error);
        return;
      }

      if (data && data.length > 0) {
        const settings = data[0];
        setFromDate(settings.from_date);
        setToDate(settings.to_date);
        setCompanyName(settings.company_name || 'PT. Example Company');
      }
    } catch (error) {
      console.error('Error loading report settings:', error);
    }
  };

  // Save report settings to Supabase
  const saveReportSettings = async () => {
    if (!session?.user?.id) return;

    try {
      const { data, error } = await supabase.rpc('save_report_settings', {
        p_report_type: reportType,
        p_from_date: fromDate,
        p_to_date: toDate,
        p_company_name: companyName,
        p_settings: {
          currency: 'IDR',
          format: 'standard'
        }
      });

      if (error) {
        console.error('Error saving report settings:', error);
        return;
      }

      console.log('Report settings saved successfully');
    } catch (error) {
      console.error('Error saving report settings:', error);
    }
  };

  // Fetch balance sheet data from API
  const fetchBalanceSheet = async () => {
    if (!session?.access_token) {
      toast.error('Anda harus login terlebih dahulu');
      return;
    }

    setLoading(true);
    try {
      // Save settings before fetching data
      await saveReportSettings();

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      };

      const url = new URL('https://wabot-n8n.libslm.easypanel.host/webhook/b7c617da-1931-4bfe-a581-ad4c27cc13b8');
      url.searchParams.append('from_date', fromDate);
      url.searchParams.append('to_date', toDate);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setBalanceSheetData(data);
      toast.success('Laporan neraca berhasil dimuat');
    } catch (error) {
      console.error('Error fetching balance sheet:', error);
      toast.error('Gagal memuat laporan neraca');
      setBalanceSheetData(null);
    } finally {
      setLoading(false);
    }
  };

  // Load settings on component mount
  useEffect(() => {
    if (session?.user?.id) {
      loadReportSettings();
    }
  }, [session?.user?.id]);

  const handleGenerateReport = () => {
    if (reportType === 'balance-sheet') {
      fetchBalanceSheet();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Demo Switcher */}
      <div className="mb-6">
        <DemoSwitcher
          currentRole={userRole}
          currentPlan={userPlan}
          onRoleChange={setUserRole}
          onPlanChange={setUserPlan}
        />
      </div>

      <div className="text-center mb-8">
        <FileText className="w-12 h-12 mx-auto mb-4 text-primary" />
        <h1 className="text-3xl font-bold mb-2">Laporan Keuangan</h1>
        <div className="flex items-center justify-center gap-4 text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-muted rounded"></div>
            <span>{companyName}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>{fromDate} - {toDate}</span>
          </div>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Report Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Konfigurasi Laporan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company-name">Nama Perusahaan</Label>
                <Input
                  id="company-name"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  placeholder="Masukkan nama perusahaan"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="report-type">Jenis Laporan</Label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih laporan keuangan" />
                  </SelectTrigger>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="from-date">Tanggal Mulai</Label>
                <Input
                  id="from-date"
                  type="date"
                  value={fromDate}
                  onChange={(e) => setFromDate(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="to-date">Tanggal Akhir</Label>
                <Input
                  id="to-date"
                  type="date"
                  value={toDate}
                  onChange={(e) => setToDate(e.target.value)}
                />
              </div>
            </div>

            {reportType && (
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2">Informasi Laporan</h3>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• Data diambil dari sistem akuntansi terintegrasi</li>
                    <li>• Laporan dibuat berdasarkan rentang tanggal yang dipilih</li>
                    <li>• Format standar akuntansi Indonesia</li>
                    <li>• Data real-time dari transaksi terkini</li>
                  </ul>
                </CardContent>
              </Card>
            )}

            <Button
              className="w-full"
              size="lg"
              onClick={handleGenerateReport}
              disabled={!reportType || loading}
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <FileText className="w-4 h-4 mr-2" />
              )}
              {loading ? 'Memuat Laporan...' : 'Tampilkan Laporan Neraca'}
            </Button>
          </CardContent>
        </Card>

        {/* Balance Sheet Display */}
        {balanceSheetData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Laporan Neraca
              </CardTitle>
              {balanceSheetData.metadata && (
                <div className="text-sm text-muted-foreground">
                  <p><strong>Organisasi:</strong> {balanceSheetData.metadata.organization_name}</p>
                  <p><strong>Mata Uang:</strong> {balanceSheetData.metadata.currency}</p>
                  <p><strong>Periode:</strong> {balanceSheetData.query?.date_range}</p>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-semibold">Akun</th>
                      <th className="text-right p-3 font-semibold">Jumlah</th>
                    </tr>
                  </thead>
                  <tbody>
                    {balanceSheetData.table?.map((item, index) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="p-3">{item.name}</td>
                        <td className="p-3 text-right font-mono">
                          {new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: balanceSheetData.metadata?.currency || 'IDR'
                          }).format(item.total)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* No Data State */}
        {!loading && !balanceSheetData && (
          <Card>
            <CardContent className="p-8 text-center">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Belum Ada Data Laporan</h3>
              <p className="text-muted-foreground mb-4">
                Klik tombol "Tampilkan Laporan Neraca" untuk memuat data laporan keuangan.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
